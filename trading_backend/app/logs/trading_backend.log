INFO - 2025-08-20 23:17:48,518 - app.manager - __init__:29 - Backend manager initialized successfully
INFO - 2025-08-20 23:17:48,522 - app.communicator - __init__:78 - Backend communicator initialized successfully
INFO - 2025-08-20 23:17:48,522 - app.communicator - lifespan:113 - Starting trading backend...listening to clients...
INFO - 2025-08-20 23:19:18,557 - app.client_instance - __init__:31 - Initializing client instance: client100
INFO - 2025-08-20 23:19:18,558 - mattlibrary.trading.execution_data_engines.simulated_execution_engine - __init__:27 - SimulatedExecutionEngine initialized
INFO - 2025-08-20 23:19:18,668 - mattlibrary.trading.fx_converter - load_fx_data:69 - Loaded fx conversion data (4553 rows) from trading_backend/app/data/fx_converter_data.parquet
INFO - 2025-08-20 23:19:18,675 - mattlibrary.trading.symbol_database - _load_symbols_from_parquet:53 - Loaded 28 symbols from trading_backend/app/data/symbol_database.parquet
INFO - 2025-08-20 23:19:18,675 - mattlibrary.trading.trading_engine - load_positions_from_disk:170 - 1 Positions loaded from disk: trading_backend/app/data/positions_client100.json
INFO - 2025-08-20 23:19:18,675 - mattlibrary.trading.trading_engine - __init__:99 - Initialized Trading Engine [base_currency=USD, starting_balance=100000 USD, track_performance=True, execution_plugin_type=SimulatedExecutionEngine, static_data_directory=trading_backend/app/data/]
INFO - 2025-08-20 23:19:18,675 - mattlibrary.datamanagement.interactive_brokers - __init__:19 - Initializing InteractiveBrokersAPI
INFO - 2025-08-20 23:19:18,676 - app.client_instance - __init__:55 - Client instance initialized successfully: client100 with IB client ID 963
INFO - 2025-08-20 23:19:18,676 - app.manager - create_client_instance:43 - Successfully created new client instance: client100
INFO - 2025-08-20 23:19:18,679 - app.client_instance - get_positions:71 - Retrieved 1 positions for client client100
INFO - 2025-08-20 23:19:18,679 - app.communicator - get_client_positions:295 - Positions requested for client client100. Total positions: 1
INFO - 2025-08-20 23:19:19,077 - app.communicator - open_websocket:396 - WebSocket connected for client: client100
INFO - 2025-08-20 23:19:23,811 - ib_async.client - connectAsync:209 - Connecting to 127.0.0.1:7497 with clientId 963...
INFO - 2025-08-20 23:19:23,812 - ib_async.client - connectAsync:218 - Connected
INFO - 2025-08-20 23:19:23,815 - ib_async.client - _onSocketHasData:396 - Logged on to server version 178
INFO - 2025-08-20 23:19:23,862 - ib_async.wrapper - error:1562 - Warning 2104, reqId -1: Market data farm connection is OK:usfarm.nj
INFO - 2025-08-20 23:19:23,862 - ib_async.wrapper - error:1562 - Warning 2104, reqId -1: Market data farm connection is OK:usfuture
INFO - 2025-08-20 23:19:23,862 - ib_async.wrapper - error:1562 - Warning 2104, reqId -1: Market data farm connection is OK:cashfarm
INFO - 2025-08-20 23:19:23,862 - ib_async.wrapper - error:1562 - Warning 2104, reqId -1: Market data farm connection is OK:usfarm
INFO - 2025-08-20 23:19:23,862 - ib_async.wrapper - error:1562 - Warning 2106, reqId -1: HMDS data farm connection is OK:euhmds
INFO - 2025-08-20 23:19:23,862 - ib_async.wrapper - error:1562 - Warning 2106, reqId -1: HMDS data farm connection is OK:cashhmds
INFO - 2025-08-20 23:19:23,862 - ib_async.wrapper - error:1562 - Warning 2106, reqId -1: HMDS data farm connection is OK:fundfarm
INFO - 2025-08-20 23:19:23,862 - ib_async.wrapper - error:1562 - Warning 2106, reqId -1: HMDS data farm connection is OK:ushmds
INFO - 2025-08-20 23:19:23,862 - ib_async.wrapper - error:1562 - Warning 2158, reqId -1: Sec-def data farm connection is OK:secdefil
INFO - 2025-08-20 23:19:23,862 - ib_async.client - connectAsync:229 - API connection ready
INFO - 2025-08-20 23:19:23,967 - ib_async.ib - connectAsync:2102 - Synchronization complete
INFO - 2025-08-20 23:19:23,967 - mattlibrary.datamanagement.interactive_brokers - connect:43 - Connected to Interactive Brokers
INFO - 2025-08-20 23:19:24,561 - mattlibrary.trading.trading_engine - save_positions_to_disk:187 - 1 Positions saved to disk: trading_backend/app/data/positions_client100.json
INFO - 2025-08-20 23:19:24,561 - app.client_instance - refresh_market_data:142 - Market data refreshed for client client100 symbols: {'FOREX_USDJPY'}
INFO - 2025-08-20 23:19:24,561 - ib_async.ib - disconnect:401 - Disconnecting from 127.0.0.1:7497, 202 B sent in 9 messages, 21.3 kB received in 451 messages, session time 5.89 s.
INFO - 2025-08-20 23:19:24,561 - ib_async.client - disconnect:241 - Disconnecting
INFO - 2025-08-20 23:19:24,561 - mattlibrary.datamanagement.interactive_brokers - disconnect:51 - Disconnected from Interactive Brokers
INFO - 2025-08-20 23:19:24,562 - ib_async.client - _onSocketDisconnected:422 - Disconnected.
