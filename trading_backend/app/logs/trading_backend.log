INFO - 2025-08-20 22:42:32,677 - app.manager - __init__:29 - Backend manager initialized successfully
INFO - 2025-08-20 22:42:32,680 - app.communicator - __init__:78 - Backend communicator initialized successfully
INFO - 2025-08-20 22:42:32,681 - app.communicator - lifespan:113 - Starting trading backend...listening to clients...
INFO - 2025-08-20 22:43:31,083 - app.client_instance - __init__:31 - Initializing client instance: client100
INFO - 2025-08-20 22:43:31,083 - mattlibrary.trading.execution_data_engines.simulated_execution_engine - __init__:27 - SimulatedExecutionEngine initialized
INFO - 2025-08-20 22:43:31,170 - mattlibrary.trading.fx_converter - load_fx_data:69 - Loaded fx conversion data (4553 rows) from trading_backend/app/data/fx_converter_data.parquet
INFO - 2025-08-20 22:43:31,179 - mattlibrary.trading.symbol_database - _load_symbols_from_parquet:53 - Loaded 28 symbols from trading_backend/app/data/symbol_database.parquet
INFO - 2025-08-20 22:43:31,179 - mattlibrary.trading.trading_engine - load_positions_from_disk:170 - 1 Positions loaded from disk: trading_backend/app/data/positions_client100.json
INFO - 2025-08-20 22:43:31,179 - mattlibrary.trading.trading_engine - __init__:99 - Initialized Trading Engine [base_currency=USD, starting_balance=100000 USD, track_performance=True, execution_plugin_type=SimulatedExecutionEngine, static_data_directory=trading_backend/app/data/]
INFO - 2025-08-20 22:43:31,179 - mattlibrary.datamanagement.interactive_brokers - __init__:19 - Initializing InteractiveBrokersAPI
INFO - 2025-08-20 22:43:31,180 - app.client_instance - __init__:55 - Client instance initialized successfully: client100 with IB client ID 963
INFO - 2025-08-20 22:43:31,180 - app.manager - create_client_instance:43 - Successfully created new client instance: client100
INFO - 2025-08-20 22:43:31,184 - app.client_instance - get_positions:71 - Retrieved 1 positions for client client100
INFO - 2025-08-20 22:43:31,184 - app.communicator - get_client_positions:295 - Positions requested for client client100. Total positions: 1
INFO - 2025-08-20 22:43:31,585 - app.communicator - open_websocket:396 - WebSocket connected for client: client100
INFO - 2025-08-20 22:44:00,697 - ib_async.client - connectAsync:209 - Connecting to 127.0.0.1:7497 with clientId 963...
INFO - 2025-08-20 22:44:00,698 - ib_async.client - connectAsync:218 - Connected
INFO - 2025-08-20 22:44:00,702 - ib_async.client - _onSocketHasData:396 - Logged on to server version 178
INFO - 2025-08-20 22:44:00,751 - ib_async.wrapper - error:1562 - Warning 2104, reqId -1: Market data farm connection is OK:usfarm.nj
INFO - 2025-08-20 22:44:00,752 - ib_async.wrapper - error:1562 - Warning 2104, reqId -1: Market data farm connection is OK:usfuture
INFO - 2025-08-20 22:44:00,752 - ib_async.wrapper - error:1562 - Warning 2104, reqId -1: Market data farm connection is OK:cashfarm
INFO - 2025-08-20 22:44:00,752 - ib_async.wrapper - error:1562 - Warning 2104, reqId -1: Market data farm connection is OK:usfarm
INFO - 2025-08-20 22:44:00,752 - ib_async.wrapper - error:1562 - Warning 2106, reqId -1: HMDS data farm connection is OK:euhmds
INFO - 2025-08-20 22:44:00,752 - ib_async.wrapper - error:1562 - Warning 2106, reqId -1: HMDS data farm connection is OK:cashhmds
INFO - 2025-08-20 22:44:00,752 - ib_async.wrapper - error:1562 - Warning 2106, reqId -1: HMDS data farm connection is OK:fundfarm
INFO - 2025-08-20 22:44:00,752 - ib_async.wrapper - error:1562 - Warning 2106, reqId -1: HMDS data farm connection is OK:ushmds
INFO - 2025-08-20 22:44:00,752 - ib_async.wrapper - error:1562 - Warning 2158, reqId -1: Sec-def data farm connection is OK:secdefil
INFO - 2025-08-20 22:44:00,752 - ib_async.client - connectAsync:229 - API connection ready
INFO - 2025-08-20 22:44:00,856 - ib_async.ib - connectAsync:2102 - Synchronization complete
INFO - 2025-08-20 22:44:00,857 - mattlibrary.datamanagement.interactive_brokers - connect:43 - Connected to Interactive Brokers
INFO - 2025-08-20 22:44:01,427 - mattlibrary.trading.trading_engine - save_positions_to_disk:187 - 1 Positions saved to disk: trading_backend/app/data/positions_client100.json
INFO - 2025-08-20 22:44:01,427 - app.client_instance - refresh_market_data:142 - Market data refreshed for client client100 symbols: {'FOREX_USDJPY'}
INFO - 2025-08-20 22:44:01,427 - ib_async.ib - disconnect:401 - Disconnecting from 127.0.0.1:7497, 202 B sent in 9 messages, 21.3 kB received in 451 messages, session time 30.2 s.
INFO - 2025-08-20 22:44:01,427 - ib_async.client - disconnect:241 - Disconnecting
INFO - 2025-08-20 22:44:01,428 - mattlibrary.datamanagement.interactive_brokers - disconnect:51 - Disconnected from Interactive Brokers
INFO - 2025-08-20 22:44:01,428 - ib_async.client - _onSocketDisconnected:422 - Disconnected.
INFO - 2025-08-20 22:48:43,685 - ib_async.client - connectAsync:209 - Connecting to 127.0.0.1:7497 with clientId 963...
INFO - 2025-08-20 22:48:43,685 - ib_async.client - connectAsync:218 - Connected
INFO - 2025-08-20 22:48:43,688 - ib_async.client - _onSocketHasData:396 - Logged on to server version 178
INFO - 2025-08-20 22:48:43,731 - ib_async.wrapper - error:1562 - Warning 2104, reqId -1: Market data farm connection is OK:usfarm.nj
INFO - 2025-08-20 22:48:43,732 - ib_async.wrapper - error:1562 - Warning 2104, reqId -1: Market data farm connection is OK:usfuture
INFO - 2025-08-20 22:48:43,732 - ib_async.wrapper - error:1562 - Warning 2104, reqId -1: Market data farm connection is OK:cashfarm
INFO - 2025-08-20 22:48:43,732 - ib_async.wrapper - error:1562 - Warning 2104, reqId -1: Market data farm connection is OK:usfarm
INFO - 2025-08-20 22:48:43,732 - ib_async.wrapper - error:1562 - Warning 2106, reqId -1: HMDS data farm connection is OK:euhmds
INFO - 2025-08-20 22:48:43,732 - ib_async.wrapper - error:1562 - Warning 2106, reqId -1: HMDS data farm connection is OK:cashhmds
INFO - 2025-08-20 22:48:43,732 - ib_async.wrapper - error:1562 - Warning 2106, reqId -1: HMDS data farm connection is OK:fundfarm
INFO - 2025-08-20 22:48:43,732 - ib_async.wrapper - error:1562 - Warning 2106, reqId -1: HMDS data farm connection is OK:ushmds
INFO - 2025-08-20 22:48:43,732 - ib_async.wrapper - error:1562 - Warning 2158, reqId -1: Sec-def data farm connection is OK:secdefil
INFO - 2025-08-20 22:48:43,732 - ib_async.client - connectAsync:229 - API connection ready
INFO - 2025-08-20 22:48:43,836 - ib_async.ib - connectAsync:2102 - Synchronization complete
INFO - 2025-08-20 22:48:43,836 - mattlibrary.datamanagement.interactive_brokers - connect:43 - Connected to Interactive Brokers
INFO - 2025-08-20 22:48:44,405 - mattlibrary.trading.trading_engine - save_positions_to_disk:187 - 1 Positions saved to disk: trading_backend/app/data/positions_client100.json
INFO - 2025-08-20 22:48:44,406 - app.client_instance - refresh_market_data:142 - Market data refreshed for client client100 symbols: {'FOREX_USDJPY'}
INFO - 2025-08-20 22:48:44,406 - ib_async.ib - disconnect:401 - Disconnecting from 127.0.0.1:7497, 202 B sent in 9 messages, 21.3 kB received in 451 messages, session time 283 s.
INFO - 2025-08-20 22:48:44,406 - ib_async.client - disconnect:241 - Disconnecting
INFO - 2025-08-20 22:48:44,406 - mattlibrary.datamanagement.interactive_brokers - disconnect:51 - Disconnected from Interactive Brokers
INFO - 2025-08-20 22:48:44,406 - ib_async.client - _onSocketDisconnected:422 - Disconnected.
INFO - 2025-08-20 22:50:13,117 - ib_async.client - connectAsync:209 - Connecting to 127.0.0.1:7497 with clientId 963...
INFO - 2025-08-20 22:50:13,118 - ib_async.client - connectAsync:218 - Connected
INFO - 2025-08-20 22:50:13,121 - ib_async.client - _onSocketHasData:396 - Logged on to server version 178
INFO - 2025-08-20 22:50:13,171 - ib_async.wrapper - error:1562 - Warning 2104, reqId -1: Market data farm connection is OK:usfarm.nj
INFO - 2025-08-20 22:50:13,172 - ib_async.wrapper - error:1562 - Warning 2104, reqId -1: Market data farm connection is OK:usfuture
INFO - 2025-08-20 22:50:13,172 - ib_async.wrapper - error:1562 - Warning 2104, reqId -1: Market data farm connection is OK:cashfarm
INFO - 2025-08-20 22:50:13,172 - ib_async.wrapper - error:1562 - Warning 2104, reqId -1: Market data farm connection is OK:usfarm
INFO - 2025-08-20 22:50:13,172 - ib_async.wrapper - error:1562 - Warning 2106, reqId -1: HMDS data farm connection is OK:euhmds
INFO - 2025-08-20 22:50:13,172 - ib_async.wrapper - error:1562 - Warning 2106, reqId -1: HMDS data farm connection is OK:cashhmds
INFO - 2025-08-20 22:50:13,172 - ib_async.wrapper - error:1562 - Warning 2106, reqId -1: HMDS data farm connection is OK:fundfarm
INFO - 2025-08-20 22:50:13,172 - ib_async.wrapper - error:1562 - Warning 2106, reqId -1: HMDS data farm connection is OK:ushmds
INFO - 2025-08-20 22:50:13,172 - ib_async.wrapper - error:1562 - Warning 2158, reqId -1: Sec-def data farm connection is OK:secdefil
INFO - 2025-08-20 22:50:13,172 - ib_async.client - connectAsync:229 - API connection ready
INFO - 2025-08-20 22:50:13,276 - ib_async.ib - connectAsync:2102 - Synchronization complete
INFO - 2025-08-20 22:50:13,276 - mattlibrary.datamanagement.interactive_brokers - connect:43 - Connected to Interactive Brokers
INFO - 2025-08-20 22:50:13,850 - mattlibrary.trading.trading_engine - save_positions_to_disk:187 - 1 Positions saved to disk: trading_backend/app/data/positions_client100.json
INFO - 2025-08-20 22:50:13,850 - app.client_instance - refresh_market_data:142 - Market data refreshed for client client100 symbols: {'FOREX_USDJPY'}
INFO - 2025-08-20 22:50:13,850 - ib_async.ib - disconnect:401 - Disconnecting from 127.0.0.1:7497, 202 B sent in 9 messages, 21.4 kB received in 451 messages, session time 89.4 s.
INFO - 2025-08-20 22:50:13,850 - ib_async.client - disconnect:241 - Disconnecting
INFO - 2025-08-20 22:50:13,850 - mattlibrary.datamanagement.interactive_brokers - disconnect:51 - Disconnected from Interactive Brokers
INFO - 2025-08-20 22:50:13,851 - ib_async.client - _onSocketDisconnected:422 - Disconnected.
INFO - 2025-08-20 23:02:18,997 - ib_async.client - connectAsync:209 - Connecting to 127.0.0.1:7497 with clientId 963...
INFO - 2025-08-20 23:02:18,998 - ib_async.client - connectAsync:218 - Connected
INFO - 2025-08-20 23:02:19,000 - ib_async.client - _onSocketHasData:396 - Logged on to server version 178
INFO - 2025-08-20 23:02:19,041 - ib_async.wrapper - error:1562 - Warning 2104, reqId -1: Market data farm connection is OK:usfarm.nj
INFO - 2025-08-20 23:02:19,041 - ib_async.wrapper - error:1562 - Warning 2104, reqId -1: Market data farm connection is OK:usfuture
INFO - 2025-08-20 23:02:19,042 - ib_async.wrapper - error:1562 - Warning 2104, reqId -1: Market data farm connection is OK:cashfarm
INFO - 2025-08-20 23:02:19,042 - ib_async.wrapper - error:1562 - Warning 2104, reqId -1: Market data farm connection is OK:usfarm
INFO - 2025-08-20 23:02:19,042 - ib_async.wrapper - error:1562 - Warning 2106, reqId -1: HMDS data farm connection is OK:euhmds
INFO - 2025-08-20 23:02:19,042 - ib_async.wrapper - error:1562 - Warning 2106, reqId -1: HMDS data farm connection is OK:cashhmds
INFO - 2025-08-20 23:02:19,042 - ib_async.wrapper - error:1562 - Warning 2106, reqId -1: HMDS data farm connection is OK:fundfarm
INFO - 2025-08-20 23:02:19,042 - ib_async.wrapper - error:1562 - Warning 2106, reqId -1: HMDS data farm connection is OK:ushmds
INFO - 2025-08-20 23:02:19,042 - ib_async.wrapper - error:1562 - Warning 2158, reqId -1: Sec-def data farm connection is OK:secdefil
INFO - 2025-08-20 23:02:19,042 - ib_async.client - connectAsync:229 - API connection ready
INFO - 2025-08-20 23:02:19,146 - ib_async.ib - connectAsync:2102 - Synchronization complete
INFO - 2025-08-20 23:02:19,146 - mattlibrary.datamanagement.interactive_brokers - connect:43 - Connected to Interactive Brokers
INFO - 2025-08-20 23:02:19,718 - mattlibrary.trading.trading_engine - save_positions_to_disk:187 - 1 Positions saved to disk: trading_backend/app/data/positions_client100.json
INFO - 2025-08-20 23:02:19,718 - app.client_instance - refresh_market_data:142 - Market data refreshed for client client100 symbols: {'FOREX_USDJPY'}
INFO - 2025-08-20 23:02:19,719 - ib_async.ib - disconnect:401 - Disconnecting from 127.0.0.1:7497, 202 B sent in 9 messages, 21.4 kB received in 451 messages, session time 726 s.
INFO - 2025-08-20 23:02:19,719 - ib_async.client - disconnect:241 - Disconnecting
INFO - 2025-08-20 23:02:19,719 - mattlibrary.datamanagement.interactive_brokers - disconnect:51 - Disconnected from Interactive Brokers
INFO - 2025-08-20 23:02:19,719 - ib_async.client - _onSocketDisconnected:422 - Disconnected.
INFO - 2025-08-20 23:06:09,334 - app.communicator - open_websocket:437 - WebSocket client disconnected: client100
WARNING - 2025-08-20 23:06:12,325 - app.manager - create_client_instance:38 - Client client100 already exists - keep existing client
INFO - 2025-08-20 23:06:12,328 - app.client_instance - get_positions:71 - Retrieved 1 positions for client client100
INFO - 2025-08-20 23:06:12,328 - app.communicator - get_client_positions:295 - Positions requested for client client100. Total positions: 1
INFO - 2025-08-20 23:06:12,727 - app.communicator - open_websocket:396 - WebSocket connected for client: client100
INFO - 2025-08-20 23:06:25,056 - app.communicator - open_websocket:437 - WebSocket client disconnected: client100
WARNING - 2025-08-20 23:08:25,189 - app.manager - create_client_instance:38 - Client client100 already exists - keep existing client
INFO - 2025-08-20 23:08:25,192 - app.client_instance - get_positions:71 - Retrieved 1 positions for client client100
INFO - 2025-08-20 23:08:25,192 - app.communicator - get_client_positions:295 - Positions requested for client client100. Total positions: 1
INFO - 2025-08-20 23:08:25,595 - app.communicator - open_websocket:396 - WebSocket connected for client: client100
INFO - 2025-08-20 23:09:12,327 - app.communicator - open_websocket:437 - WebSocket client disconnected: client100
WARNING - 2025-08-20 23:09:19,331 - app.manager - create_client_instance:38 - Client client100 already exists - keep existing client
INFO - 2025-08-20 23:09:19,334 - app.client_instance - get_positions:71 - Retrieved 1 positions for client client100
INFO - 2025-08-20 23:09:19,335 - app.communicator - get_client_positions:295 - Positions requested for client client100. Total positions: 1
INFO - 2025-08-20 23:09:19,735 - app.communicator - open_websocket:396 - WebSocket connected for client: client100
INFO - 2025-08-20 23:09:54,186 - app.communicator - open_websocket:437 - WebSocket client disconnected: client100
WARNING - 2025-08-20 23:10:11,509 - app.manager - create_client_instance:38 - Client client100 already exists - keep existing client
INFO - 2025-08-20 23:10:11,512 - app.client_instance - get_positions:71 - Retrieved 1 positions for client client100
INFO - 2025-08-20 23:10:11,512 - app.communicator - get_client_positions:295 - Positions requested for client client100. Total positions: 1
INFO - 2025-08-20 23:10:11,911 - app.communicator - open_websocket:396 - WebSocket connected for client: client100
INFO - 2025-08-20 23:10:21,005 - app.communicator - open_websocket:437 - WebSocket client disconnected: client100
WARNING - 2025-08-20 23:12:03,563 - app.manager - create_client_instance:38 - Client client100 already exists - keep existing client
INFO - 2025-08-20 23:12:03,566 - app.client_instance - get_positions:71 - Retrieved 1 positions for client client100
INFO - 2025-08-20 23:12:03,567 - app.communicator - get_client_positions:295 - Positions requested for client client100. Total positions: 1
INFO - 2025-08-20 23:12:03,967 - app.communicator - open_websocket:396 - WebSocket connected for client: client100
