<script lang="ts">
  import { symbolsStore } from '$lib/stores/symbols';
  import { submitOrder } from '$lib/api';
  import type { OrderRequest } from '$lib/types';

  // Props
  let { isOpen = $bindable(false) } = $props();

  // Form state
  let strategyId = $state('');
  let symbolId = $state('');
  let size = $state(0);
  let orderType = $state('market');
  let limitPrice = $state(0);
  let stopPrice = $state(0);
  let customDatetime = $state('');

  // Form validation and UI state
  let isSubmitting = $state(false);
  let errorMessage = $state('');

  // Order type options - display names mapped to backend values
  const orderTypes = [
    { label: 'Market', value: 'market' },
    { label: 'Limit', value: 'limit' },
    { label: 'Stop', value: 'stop' },
    { label: 'Stop Limit', value: 'stoplimit' }
  ];

  // Current date/time for default value
  const currentDateTime = $derived(() => {
    const now = new Date();
    const year = now.getFullYear();
    const month = String(now.getMonth() + 1).padStart(2, '0');
    const day = String(now.getDate()).padStart(2, '0');
    const hours = String(now.getHours()).padStart(2, '0');
    const minutes = String(now.getMinutes()).padStart(2, '0');
    return `${year}-${month}-${day}T${hours}:${minutes}`;
  });

  // Initialize datetime when modal opens
  $effect(() => {
    if (isOpen && !customDatetime) {
      customDatetime = currentDateTime();
    }
  });

  // Check if limit price should be disabled
  const isLimitPriceDisabled = $derived(orderType === 'market' || orderType === 'stop');
  
  // Check if stop price should be disabled
  const isStopPriceDisabled = $derived(orderType === 'market' || orderType === 'limit');

  // Symbol options computed from store
  let symbolOptions = $state<string[]>([]);
  $effect(() => {
    symbolOptions = Object.keys($symbolsStore.symbols);
  });

  // State and behavior for a filterable combobox for Symbol ID
  let symbolSearch = $state(''); // what the user types
  let isSymbolMenuOpen = $state(false); // dropdown visibility
  let highlightedIndex = $state(0); // keyboard highlight index

  // Compute filtered symbols by case-insensitive substring match
  // Filtered symbols derived from symbolOptions value
  const filteredSymbols = $derived(() => {
    const q = symbolSearch.trim().toLowerCase();
    const list = symbolOptions as string[];
    return q ? list.filter((s) => s.toLowerCase().includes(q)) : list;
  });

  // Select a symbol from the list
  function selectSymbol(sym: string) {
    symbolId = sym; // set real form value
    symbolSearch = sym; // reflect selection in the input
    isSymbolMenuOpen = false; // close dropdown
  }

  // Handle keyboard navigation within the combobox
  function handleSymbolKeydown(event: KeyboardEvent) {
    const list = filteredSymbols; // current filtered set
    if (!list.length) return; // nothing to navigate
    if (event.key === 'ArrowDown') {
      event.preventDefault();
      highlightedIndex = (highlightedIndex + 1) % list.length; // move down
    } else if (event.key === 'ArrowUp') {
      event.preventDefault();
      highlightedIndex = (highlightedIndex - 1 + list.length) % list.length; // move up
    } else if (event.key === 'Enter') {
      event.preventDefault();
      selectSymbol(list[highlightedIndex]); // choose highlighted
    } else if (event.key === 'Escape') {
      isSymbolMenuOpen = false; // close on escape
    }
  }

  // Reset form when modal closes
  $effect(() => {
    if (!isOpen) {
      strategyId = '';
      symbolId = '';
      size = 0;
      orderType = 'market';
      limitPrice = 0;
      stopPrice = 0;
      customDatetime = '';
      errorMessage = '';
    }
  });

  async function handleSubmit() {
    if (isSubmitting) return;

    // Basic validation
    if (!strategyId.trim()) {
      errorMessage = 'Strategy ID is required';
      return;
    }
    if (!symbolId) {
      errorMessage = 'Symbol ID is required';
      return;
    }
    if (size === 0) {
      errorMessage = 'Size cannot be zero';
      return;
    }
    if (!customDatetime) {
      errorMessage = 'Custom timestamp is required';
      return;
    }

    isSubmitting = true;
    errorMessage = '';

    try {
      const orderRequest: OrderRequest = {
        strategy_id: strategyId.trim(),
        symbol_id: symbolId,
        size: size,
        order_type: orderType,
        limit_price: limitPrice,
        stop_price: stopPrice,
        custom_datetime: customDatetime
      };

      const response = await submitOrder(orderRequest);
      
      if (response.success) {
        // Close modal on success
        isOpen = false;
        // You could add a success notification here
        console.log('Order submitted successfully:', response.order_id);
      } else {
        errorMessage = response.message || 'Failed to submit order';
      }
    } catch (error) {
      errorMessage = error instanceof Error ? error.message : 'An unknown error occurred';
    } finally {
      isSubmitting = false;
    }
  }

  function handleCancel() {
    isOpen = false;
  }

  // Handle escape key
  function handleKeydown(event: KeyboardEvent) {
    if (event.key === 'Escape') {
      handleCancel();
    }
  }
</script>

{#if isOpen}
  <!-- Modal backdrop -->
  <div class="modal-backdrop" onclick={handleCancel} onkeydown={handleKeydown} role="presentation">
    <!-- Modal content -->
    <div class="modal-content" onclick={(e) => e.stopPropagation()} onkeydown={handleKeydown} role="dialog" aria-modal="true" aria-labelledby="modal-title" tabindex="-1">
      <div class="modal-header">
        <h2 id="modal-title">Create Order</h2>
        <button class="close-button" onclick={handleCancel} aria-label="Close modal">×</button>
      </div>

      <form class="modal-body" onsubmit={(e) => { e.preventDefault(); handleSubmit(); }}>
        {#if errorMessage}
          <div class="error-message">
            {errorMessage}
          </div>
        {/if}

        <div class="form-group">
          <label for="strategy-id">Strategy ID</label>
          <input
            id="strategy-id"
            type="text"
            bind:value={strategyId}
            placeholder="Enter strategy ID"
            required
          />
        </div>

        <div class="form-group">
          <label for="symbol-id">Symbol ID</label>
          <!-- Filterable combobox: input + results list -->
          <div class="combobox">
            <input
              id="symbol-id"
              name="symbol-id"
              type="text"
              role="combobox"
              aria-expanded={isSymbolMenuOpen}
              aria-controls="symbol-listbox"
              aria-autocomplete="list"
              placeholder="Type to search symbols"
              bind:value={symbolSearch}
              onfocus={() => { isSymbolMenuOpen = true; highlightedIndex = 0; }}
              oninput={() => { isSymbolMenuOpen = true; highlightedIndex = 0; symbolId = symbolSearch; }}
              onkeydown={handleSymbolKeydown}
              required
            />
            {#if isSymbolMenuOpen}
              <ul id="symbol-listbox" class="combo-list" role="listbox">
                {#if filteredSymbols.length === 0}
                  <li class="combo-empty">No matches</li>
                {:else}
                  {#each filteredSymbols as sym, i}
                    <li
                      role="option"
                      class="combo-option {i === highlightedIndex ? 'highlighted' : ''}"
                      aria-selected={i === highlightedIndex}
                      onclick={() => selectSymbol(sym)}
                      onmouseenter={() => highlightedIndex = i}
                      onkeydown={(e) => { if (e.key === 'Enter' || e.key === ' ') selectSymbol(sym); }}
                      tabindex="0"
                    >{sym}</li>
                  {/each}
                {/if}
              </ul>
            {/if}
          </div>
        </div>

        <div class="form-group">
          <label for="size">Size</label>
          <input
            id="size"
            type="number"
            bind:value={size}
            placeholder="Enter size (+ for buy, - for sell)"
            required
          />
        </div>

        <div class="form-group">
          <label for="order-type">Order Type</label>
          <select id="order-type" bind:value={orderType} required>
            {#each orderTypes as type}
              <option value={type.value}>{type.label}</option>
            {/each}
          </select>
        </div>

        <div class="form-group">
          <label for="limit-price">Limit Price</label>
          <input
            id="limit-price"
            type="number"
            step="0.01"
            bind:value={limitPrice}
            placeholder="Enter limit price"
            disabled={isLimitPriceDisabled}
          />
        </div>

        <div class="form-group">
          <label for="stop-price">Stop Price</label>
          <input
            id="stop-price"
            type="number"
            step="0.01"
            bind:value={stopPrice}
            placeholder="Enter stop price"
            disabled={isStopPriceDisabled}
          />
        </div>

        <div class="form-group">
          <label for="custom-datetime">Custom Timestamp</label>
          <input
            id="custom-datetime"
            type="datetime-local"
            bind:value={customDatetime}
            required
          />
        </div>

        <div class="form-actions">
          <button type="submit" class="btn-submit" disabled={isSubmitting}>
            {isSubmitting ? 'Submitting...' : 'Submit'}
          </button>
          <button type="button" class="btn-cancel" onclick={handleCancel} disabled={isSubmitting}>
            Cancel
          </button>
        </div>
      </form>
    </div>
  </div>
{/if}

<style>
  .modal-backdrop {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.5);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 1000;
  }

  .modal-content {
    background: var(--surface);
    border-radius: 0.5rem;
    box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
    max-width: 500px;
    width: 90%;
    max-height: 90vh;
    overflow-y: auto;
    border: 1px solid var(--border);
  }

  .modal-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 1.5rem;
    border-bottom: 1px solid var(--border);
  }

  .modal-header h2 {
    margin: 0;
    font-size: 1.5rem;
    font-weight: 700;
    color: #f97316; /* Orange color matching the page headers */
  }

  .close-button {
    background: none;
    border: none;
    font-size: 1.5rem;
    color: var(--muted);
    cursor: pointer;
    padding: 0.25rem;
    border-radius: 0.25rem;
    transition: all 0.2s ease;
  }

  .close-button:hover {
    background: var(--hover);
    color: var(--text);
  }

  .modal-body {
    padding: 1.5rem;
  }

  .form-group {
    margin-bottom: 1.25rem;
  }

  .form-group label {
    display: block;
    margin-bottom: 0.5rem;
    font-weight: 600;
    color: var(--text);
    font-size: 0.875rem;
  }

  .form-group input,
  .form-group select {
    width: 100%;
    padding: 0.75rem;
    border: 1px solid var(--border);
    border-radius: 0.375rem;
    background: var(--background);
    color: var(--text);
    font-size: 0.875rem;
    transition: all 0.2s ease;
  }

  .form-group input:focus,
  .form-group select:focus {
    outline: none;
    border-color: #f97316; /* Orange focus color */
    box-shadow: 0 0 0 3px rgba(249, 115, 22, 0.1);
  }

  .form-group input:disabled {
    background: var(--surface-2);
    color: var(--muted);
    cursor: not-allowed;
  }

  .error-message {
    background: rgba(239, 68, 68, 0.1);
    border: 1px solid rgba(239, 68, 68, 0.3);
    color: #ef4444;
    padding: 0.75rem;
    border-radius: 0.375rem;
    margin-bottom: 1.25rem;
    font-size: 0.875rem;
  }

  .form-actions {
    display: flex;
    gap: 1rem;
    justify-content: flex-end;
    margin-top: 2rem;
    padding-top: 1.5rem;
    border-top: 1px solid var(--border);
  }

  .btn-submit,
  .btn-cancel {
    padding: 0.75rem 1.5rem;
    border-radius: 0.375rem;
    font-weight: 600;
    font-size: 0.875rem;
    cursor: pointer;
    transition: all 0.2s ease;
    border: 1px solid transparent;
  }

  .btn-submit {
    background: #f97316; /* Orange submit button */
    color: white;
    border-color: #f97316;
  }

  .btn-submit:hover:not(:disabled) {
    background: #ea580c;
    border-color: #ea580c;
  }

  .btn-submit:disabled {
    background: var(--muted);
    border-color: var(--muted);
    cursor: not-allowed;
    opacity: 0.6;
  }

  .btn-cancel {
    background: var(--surface);
    color: var(--text);
    border-color: var(--border);
  }

  .btn-cancel:hover:not(:disabled) {
    background: var(--surface-2);
    border-color: var(--accent);
  }

  .btn-cancel:disabled {
    opacity: 0.6;
    cursor: not-allowed;
  }

  @media (max-width: 640px) {
    .modal-content {
      width: 95%;
      margin: 1rem;
    }

    .modal-header,
    .modal-body {
      padding: 1rem;
    }

    .form-actions {
      flex-direction: column;
    }

    .btn-submit,
    .btn-cancel {
      width: 100%;
    }
  }
</style>
